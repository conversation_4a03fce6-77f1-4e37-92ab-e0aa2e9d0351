import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, ScrollView } from 'react-native';

export function Registration() {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false,
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleRegister = () => {
    // Handle registration logic here
    console.log('Registration data:', formData);
  };

  return (
    <ScrollView className="flex-1 bg-white">
      <View className="flex-1 px-6 py-8">
        {/* Logo Section */}
        <View className="items-center mb-8">
          <Image 
            source={require('../assets/Logo.png')} 
            className="w-32 h-16 mb-4"
            resizeMode="contain"
          />
          <Text className="text-3xl font-oxanium-bold text-gray-800 mb-2">
            Create Account
          </Text>
          <Text className="text-base font-oxanium text-gray-500 text-center">
            Sign up to access your Estate Link account
          </Text>
        </View>

        {/* Registration Form */}
        <View className="space-y-4">
          {/* Full Name */}
          <View>
            <Text className="text-sm font-oxanium-semibold text-gray-700 mb-2">
              Full Name
            </Text>
            <TextInput
              className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg font-oxanium text-gray-800"
              placeholder="Enter your full name"
              placeholderTextColor="#9CA3AF"
              value={formData.fullName}
              onChangeText={(value) => handleInputChange('fullName', value)}
            />
          </View>

          {/* Email */}
          <View>
            <Text className="text-sm font-oxanium-semibold text-gray-700 mb-2">
              Email Address
            </Text>
            <TextInput
              className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg font-oxanium text-gray-800"
              placeholder="Enter your email address"
              placeholderTextColor="#9CA3AF"
              keyboardType="email-address"
              autoCapitalize="none"
              value={formData.email}
              onChangeText={(value) => handleInputChange('email', value)}
            />
          </View>

          {/* Phone Number */}
          <View>
            <Text className="text-sm font-oxanium-semibold text-gray-700 mb-2">
              Phone Number
            </Text>
            <TextInput
              className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg font-oxanium text-gray-800"
              placeholder="Enter your phone number"
              placeholderTextColor="#9CA3AF"
              keyboardType="phone-pad"
              value={formData.phone}
              onChangeText={(value) => handleInputChange('phone', value)}
            />
          </View>

          {/* Password */}
          <View>
            <Text className="text-sm font-oxanium-semibold text-gray-700 mb-2">
              Password
            </Text>
            <TextInput
              className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg font-oxanium text-gray-800"
              placeholder="Create a password"
              placeholderTextColor="#9CA3AF"
              secureTextEntry
              value={formData.password}
              onChangeText={(value) => handleInputChange('password', value)}
            />
          </View>

          {/* Confirm Password */}
          <View>
            <Text className="text-sm font-oxanium-semibold text-gray-700 mb-2">
              Confirm Password
            </Text>
            <TextInput
              className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg font-oxanium text-gray-800"
              placeholder="Confirm your password"
              placeholderTextColor="#9CA3AF"
              secureTextEntry
              value={formData.confirmPassword}
              onChangeText={(value) => handleInputChange('confirmPassword', value)}
            />
          </View>

          {/* Terms and Conditions */}
          <View className="flex-row items-center mt-4">
            <TouchableOpacity 
              className="w-5 h-5 border-2 border-teal-500 rounded mr-3"
              onPress={() => setFormData(prev => ({ ...prev, agreeToTerms: !prev.agreeToTerms }))}
            >
              {formData.agreeToTerms && (
                <View className="w-full h-full bg-teal-500 rounded items-center justify-center">
                  <Text className="text-white text-xs">✓</Text>
                </View>
              )}
            </TouchableOpacity>
            <Text className="font-oxanium text-gray-700 flex-1">
              I agree to the{' '}
              <Text className="text-teal-500 font-oxanium-semibold">Terms & Conditions</Text>
              {' '}and{' '}
              <Text className="text-teal-500 font-oxanium-semibold">Privacy Policy</Text>
            </Text>
          </View>

          {/* Register Button */}
          <TouchableOpacity
            className="w-full bg-white border-2 border-teal-500 rounded-lg py-4 mt-6"
            onPress={handleRegister}
          >
            <Text className="text-center font-oxanium-semibold text-teal-500 text-lg">
              Create Account
            </Text>
          </TouchableOpacity>

          {/* Login Link */}
          <View className="items-center mt-6">
            <Text className="font-oxanium text-gray-600">
              Already have an account?{' '}
              <Text className="text-teal-500 font-oxanium-semibold">Login here</Text>
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
} 