import './global.css';
import { Login } from 'components/Login';
import { useFonts, Oxanium_400Regular, Oxanium_500Medium, Oxanium_600SemiBold, Oxanium_700Bold } from '@expo-google-fonts/oxanium';
import { View, Text } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import { useCallback } from 'react';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export default function App() {
  const [fontsLoaded, fontError] = useFonts({
    'Oxanium-Regular': Oxanium_400Regular,
    'Oxanium-Medium': Oxanium_500Medium,
    'Oxanium-SemiBold': Oxanium_600SemiBold,
    'Oxanium-Bold': Oxanium_700Bold,
  });

  const onLayoutRootView = useCallback(async () => {
    if (fontsLoaded || fontError) {
      await SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  if (!fontsLoaded && !fontError) {
    return null;
  }

  return (
    <View className="flex-1 bg-white" onLayout={onLayoutRootView}>
      <Login />
    </View>
  );
}


