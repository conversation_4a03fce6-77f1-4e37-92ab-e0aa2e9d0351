import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image } from 'react-native';

export function Login() {
  const [formData, setFormData] = useState({
    username: '',
    rememberMe: false,
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLogin = () => {
    // Handle login logic here
    console.log('Login data:', formData);
  };

  return (
    <View className="flex-1 bg-white">
      {/* Main Layout Container */}
      <View 
        className="mx-auto"
        style={{
          width: 393,
          height: 698,
          marginTop: 133,
          gap: 205,
        }}
      >
        {/* Logo Section */}
        <View className="items-center">
          <Image 
            source={require('../assets/Logo.png')} 
            style={{
              width: 203,
              height: 65,
            }}
            resizeMode="contain"
          />
          
          {/* Welcome Back Text */}
          <Text 
            className="text-gray-800 mb-2"
            style={{
              fontFamily: 'Oxanium-Bold',
              fontWeight: '700',
              fontSize: 30,
              textAlign: 'center',
            }}
          >
            Welcome Back
          </Text>
          
          {/* Subtitle */}
          <Text 
            className="text-gray-500 text-center"
            style={{
              fontFamily: 'Oxanium-Medium',
              fontWeight: '500',
              fontSize: 16,
            }}
          >
            Login to access your Estate Link account
          </Text>
        </View>

        {/* Form Section */}
        <View className="space-y-4">
          {/* Label */}
          <Text 
            className="text-gray-700 mb-2"
            style={{
              fontFamily: 'Oxanium-Medium',
              fontWeight: '500',
              fontSize: 16,
            }}
          >
            User Name / Email / Phone Number
          </Text>
          
          {/* Input Field */}
          <TextInput
            style={{
              width: 360,
              height: 56,
              borderRadius: 12,
              borderWidth: 1,
              borderColor: '#E5E7EB',
              paddingTop: 10,
              paddingRight: 16,
              paddingBottom: 10,
              paddingLeft: 16,
              backgroundColor: '#F9FAFB',
              fontFamily: 'Oxanium-Regular',
              fontSize: 16,
              color: '#1F2937',
            }}
            placeholder="enter your user name / e-mail / phone number"
            placeholderTextColor="#9CA3AF"
            value={formData.username}
            onChangeText={(value) => handleInputChange('username', value)}
          />

          {/* Remember Me Checkbox */}
          <View className="flex-row items-center mt-4">
            <TouchableOpacity 
              className="w-5 h-5 border-2 border-teal-500 rounded mr-3"
              onPress={() => setFormData(prev => ({ ...prev, rememberMe: !prev.rememberMe }))}
            >
              {formData.rememberMe && (
                <View className="w-full h-full bg-teal-500 rounded items-center justify-center">
                  <Text className="text-white text-xs">✓</Text>
                </View>
              )}
            </TouchableOpacity>
            <Text 
              className="text-gray-700"
              style={{
                fontFamily: 'Oxanium-Medium',
                fontWeight: '500',
                fontSize: 16,
              }}
            >
              Remember me
            </Text>
          </View>

          {/* Login Button */}
          <TouchableOpacity
            style={{
              width: 360,
              height: 56,
              backgroundColor: 'white',
              borderWidth: 2,
              borderColor: '#14B8A6',
              borderRadius: 12,
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: 24,
            }}
            onPress={handleLogin}
          >
            <Text 
              style={{
                fontFamily: 'Oxanium-SemiBold',
                fontWeight: '600',
                fontSize: 18,
                color: '#14B8A6',
              }}
            >
              Login
            </Text>
          </TouchableOpacity>

          {/* Forgot Password Link */}
          <View className="items-center mt-4">
            <Text 
              style={{
                fontFamily: 'Oxanium-SemiBold',
                fontWeight: '600',
                fontSize: 16,
                color: '#14B8A6',
              }}
            >
              Forgot Password?
            </Text>
          </View>
        </View>
      </View>

      {/* Bottom Link */}
      <View className="absolute bottom-8 left-0 right-0 items-center">
        <Text 
          style={{
            fontFamily: 'Oxanium-Regular',
            fontSize: 16,
            color: '#374151',
            textDecorationLine: 'underline',
          }}
        >
          Log into Estate Control
        </Text>
      </View>
    </View>
  );
} 